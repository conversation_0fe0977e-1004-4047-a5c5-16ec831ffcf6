# Gamification Dashboard Wireframe
**ORB Currency, Achievements, and Progression System**

## 📋 Wireframe Information
- **Purpose**: Gamification features including ORB wallet, achievements, and leaderboards
- **User Access**: All authenticated users
- **Navigation Context**: Accessible from main dashboard and spatial navigation
- **Priority**: High - Core engagement feature
- **Implementation Status**: 🔴 Not Implemented

---

## 🎯 Overview

**[Design Team: This wireframe shows the complete gamification experience]**

The Gamification Dashboard provides users with an engaging overview of their ORB currency, achievements, progression, and competitive standings within the Royaltea platform.

### **Core Functionality**
- ORB wallet and transaction history
- Achievement gallery and progress tracking
- Leaderboards and competitive rankings
- Skill progression and level advancement
- Reward redemption and marketplace

---

## 📱 Layout Wireframes

### **Desktop Layout (1200px+)**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 💎 Gamification Hub                                   [🏪 ORB Store] [⚙️ Settings] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────────────────┐ │
│ │ 💰 ORB Wallet   │ │ 🏆 Achievements │ │ 📊 Your Progress                │ │
│ │                 │ │                 │ │                                 │ │
│ │ Balance:        │ │ Recent Unlocks: │ │ Level 12 Developer              │ │
│ │ 1,247 ORBs      │ │                 │ │ ████████████░░░░ 75% to Level 13│ │
│ │                 │ │ 🥇 First        │ │                                 │ │
│ │ Value:          │ │ Alliance        │ │ Total XP: 12,450                │ │
│ │ ~$124.70 USDC   │ │ Created         │ │ This Month: +890 XP             │ │
│ │                 │ │                 │ │                                 │ │
│ │ Recent Earnings:│ │ 🎯 10 Tasks     │ │ Skill Mastery:                  │ │
│ │ +50 ORBs        │ │ Completed       │ │ React: ████████████ Expert      │ │
│ │ Project Alpha   │ │                 │ │ Node.js: ████████░░ Advanced    │ │
│ │                 │ │ 🤝 5 Successful │ │ TypeScript: ██████░░░░ Inter.   │ │
│ │ +25 ORBs        │ │ Collaborations  │ │                                 │ │
│ │ Code Review     │ │                 │ │ [View Skill Tree]               │ │
│ │                 │ │ [View All]      │ │ [Set Goals]                     │ │
│ │ [View History]  │ │ [Share Progress]│ │                                 │ │
│ │ [Cash Out]      │ │                 │ │                                 │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 🏅 Leaderboards                                                         │ │
│ │                                                                         │ │
│ │ 📅 This Month    [All Time] [This Week] [Alliance Only]                │ │
│ │                                                                         │ │
│ │ 1. 👑 Sarah Johnson      2,450 ORBs    Level 15    🔥 Streak: 12 days  │ │
│ │ 2. 🥈 Mike Chen          2,100 ORBs    Level 14    🔥 Streak: 8 days   │ │
│ │ 3. 🥉 Alex Rivera        1,890 ORBs    Level 13    🔥 Streak: 15 days  │ │
│ │ 4.    You                1,247 ORBs    Level 12    🔥 Streak: 5 days   │ │
│ │ 5.    David Kim          1,156 ORBs    Level 10    🔥 Streak: 3 days   │ │
│ │ 6.    Lisa Wong          1,089 ORBs    Level 11    🔥 Streak: 7 days   │ │
│ │                                                                         │ │
│ │ Your Rank: #4 out of 247 users (+2 from last week) ⬆️                 │ │
│ │                                                                         │ │
│ │ [View Full Leaderboard] [Alliance Rankings] [Skill-Based Rankings]     │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 🎯 Active Challenges                                                    │ │
│ │                                                                         │ │
│ │ 🚀 Weekly Challenge: Complete 5 Tasks                                  │ │
│ │ Progress: [████████░░] 4/5 tasks    Reward: 100 ORBs + Badge          │ │
│ │ Time Left: 2 days, 14 hours                                            │ │
│ │                                                                         │ │
│ │ 🤝 Monthly Challenge: Collaborate with 3 New Allies                    │ │
│ │ Progress: [██████░░░░] 2/3 allies    Reward: 250 ORBs + Title         │ │
│ │ Time Left: 18 days                                                      │ │
│ │                                                                         │ │
│ │ 🎨 Skill Challenge: Master TypeScript                                  │ │
│ │ Progress: [████████░░] 80% complete    Reward: 500 ORBs + Certification│ │
│ │ No time limit                                                           │ │
│ │                                                                         │ │
│ │ [View All Challenges] [Create Custom Challenge]                        │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Mobile Layout (< 768px)**
```
┌─────────────────────────────────────┐
│ 💎 Gamification        [🏪] [⚙️]   │
├─────────────────────────────────────┤
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 💰 ORB Wallet                   │ │
│ │                                 │ │
│ │ Balance: 1,247 ORBs             │ │
│ │ Value: ~$124.70 USDC            │ │
│ │                                 │ │
│ │ Recent: +50 ORBs (Project)      │ │
│ │                                 │ │
│ │ [View History] [Cash Out]       │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📊 Your Progress                │ │
│ │                                 │ │
│ │ Level 12 Developer              │ │
│ │ ████████████░░░░ 75% to Lv 13   │ │
│ │                                 │ │
│ │ Total XP: 12,450                │ │
│ │ This Month: +890 XP             │ │
│ │                                 │ │
│ │ [View Skills] [Set Goals]       │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 🏆 Recent Achievements          │ │
│ │                                 │ │
│ │ 🥇 First Alliance Created       │ │
│ │ 🎯 10 Tasks Completed           │ │
│ │ 🤝 5 Successful Collaborations  │ │
│ │                                 │ │
│ │ [View All Achievements]         │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 🏅 Your Ranking                 │ │
│ │                                 │ │
│ │ #4 out of 247 users             │ │
│ │ 1,247 ORBs this month           │ │
│ │ 🔥 5-day streak                 │ │
│ │                                 │ │
│ │ [View Leaderboard]              │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 🎯 Active Challenges            │ │
│ │                                 │ │
│ │ Weekly: Complete 5 Tasks        │ │
│ │ [████████░░] 4/5                │ │
│ │ Reward: 100 ORBs                │ │
│ │                                 │ │
│ │ [View All Challenges]           │ │
│ └─────────────────────────────────┘ │
│                                     │
└─────────────────────────────────────┘
```

---

## 🔄 User Flow

### **ORB Earning Flow**
```mermaid
graph TD
    A[Complete Work Task] --> B[System Calculates ORB Reward]
    B --> C[ORB Added to Wallet]
    C --> D[Achievement Check]
    D --> E[Unlock New Badge?]
    E -->|Yes| F[Show Achievement Notification]
    E -->|No| G[Update Progress Bars]
    F --> H[Share Achievement Option]
    G --> I[Continue Working]
    H --> I
```

### **Achievement Unlock Flow**
```mermaid
graph TD
    A[Trigger Achievement Condition] --> B[Check Achievement Rules]
    B --> C[Achievement Unlocked!]
    C --> D[Celebration Animation]
    D --> E[Badge Added to Collection]
    E --> F[XP Bonus Awarded]
    F --> G[Social Sharing Option]
    G --> H[Update Profile Display]
```

---

## 🧩 Component Breakdown

### **ORB Wallet Component**
- **Balance Display**: Current ORB count with USDC equivalent
- **Recent Transactions**: Latest ORB earnings with sources
- **Quick Actions**: View history, cash out, transfer ORBs
- **Value Tracking**: Real-time ORB-to-currency conversion

### **Achievement Gallery Component**
- **Recent Unlocks**: Latest achievements with celebration
- **Progress Tracking**: Partial progress toward next achievements
- **Achievement Categories**: Skill, collaboration, project-based
- **Social Sharing**: Share achievements with network

### **Progress Dashboard Component**
- **Level Display**: Current level with progress to next
- **XP Tracking**: Total and recent experience points
- **Skill Mastery**: Progress bars for different skills
- **Goal Setting**: Personal development objectives

### **Leaderboard Component**
- **Ranking Display**: User position and top performers
- **Time Periods**: Weekly, monthly, all-time rankings
- **Filtering Options**: Alliance-only, skill-based rankings
- **Streak Tracking**: Consecutive activity streaks

### **Challenge System Component**
- **Active Challenges**: Current challenges with progress
- **Time Limits**: Countdown timers for time-based challenges
- **Reward Preview**: What users can earn
- **Custom Challenges**: User-created personal goals

---

## 📱 Responsive Behavior

### **Mobile (< 768px)**
- **Single Column Layout**: Stack all components vertically
- **Simplified Metrics**: Key numbers only, detailed view on tap
- **Swipeable Achievements**: Horizontal scroll for achievement gallery
- **Condensed Leaderboard**: Top 3 + user position
- **Touch-Optimized Actions**: Large buttons for ORB actions

### **Tablet (768px - 1024px)**
- **Two Column Layout**: Wallet/progress on left, achievements/leaderboard on right
- **Expandable Sections**: Collapsible detailed views
- **Touch-Friendly Interface**: Optimized for tablet interaction
- **Grid View Achievements**: Card-based achievement display

### **Desktop (1024px+)**
- **Three Column Layout**: Wallet, achievements, progress side-by-side
- **Hover States**: Rich hover interactions and tooltips
- **Keyboard Shortcuts**: Quick navigation and actions
- **Advanced Analytics**: Detailed charts and progress tracking

---

## 🎨 Visual Design Requirements

### **Color Scheme**
- **ORB Currency**: Gold (#f59e0b) for ORB-related elements
- **Achievements**: Royal purple (#8b5cf6) for achievement highlights
- **Progress Bars**: Gradient from tea green to royal purple
- **Leaderboard**: Gold (#f59e0b) for #1, silver (#9ca3af) for #2, bronze (#d97706) for #3
- **Challenges**: Blue (#3b82f6) for active challenges

### **Typography**
- **ORB Balance**: font-bold text-2xl for primary balance display
- **Achievement Names**: font-semibold text-lg for achievement titles
- **Progress Labels**: text-base for skill and level information
- **Leaderboard Entries**: text-sm with clear hierarchy

### **Animations**
- **ORB Earning**: Coin flip animation when ORBs are earned
- **Achievement Unlock**: Celebration animation with confetti
- **Progress Updates**: Smooth progress bar animations
- **Leaderboard Changes**: Slide animations for rank changes

---

## 🔧 Technical Implementation

### **Props & State**
```javascript
const GamificationDashboard = ({
  currentUser,
  orbBalance = 0,
  achievements = [],
  leaderboard = [],
  challenges = [],
  userProgress = {},
  onCashOut,
  onShareAchievement,
  onSetGoal
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [showAchievements, setShowAchievements] = useState(false);
  const [activeChallenge, setActiveChallenge] = useState(null);
};
```

### **Data Requirements**
- **ORB Transactions**: Earning and spending history
- **Achievement Data**: Unlocked and progress toward achievements
- **User Progression**: XP, levels, skill advancement
- **Leaderboard Rankings**: User rankings across different metrics
- **Challenge Progress**: Active and completed challenges

---

## ♿ Accessibility Features

### **Screen Reader Support**
- **ORB Balance**: "Current ORB balance: 1,247 ORBs, equivalent to 124 dollars and 70 cents"
- **Achievements**: "Achievement unlocked: First Alliance Created, earned 100 experience points"
- **Progress**: "Level 12 Developer, 75% progress to Level 13"
- **Leaderboard**: "You are ranked 4th out of 247 users with 1,247 ORBs"

### **Keyboard Navigation**
- **Tab Order**: Logical tab sequence through all interactive elements
- **Keyboard Shortcuts**: 
  - `W` to view ORB wallet details
  - `A` to view achievement gallery
  - `L` to view leaderboard
  - `C` to view challenges
- **Focus Indicators**: Clear visual focus states
- **Skip Links**: Quick navigation to main content areas

---

**[Design Team: This gamification dashboard should make users feel accomplished and motivated to continue contributing to the platform. Focus on clear progress indicators, meaningful rewards, and friendly competition.]**
