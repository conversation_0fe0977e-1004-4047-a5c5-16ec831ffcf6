# Design System Assets
**Design Team: Your Assets Control the Platform**

## 🎯 **What This Directory Does**

When you add or update assets in this directory, AI agents automatically integrate them into the platform code. Your visual designs become the actual user interface.

---

## 📁 **Directory Structure**

```
design-system/
├── icons/
│   ├── system/          # Core platform icons (alliance, venture, mission, ORB)
│   ├── navigation/      # Navigation icons (home, dashboard, grid view)
│   ├── actions/         # Action buttons (create, edit, delete, share)
│   └── social/          # Social features (ally, message, endorse)
├── colors/              # Color swatches and examples (future)
├── typography/          # Font specimens (future)
└── components/          # Component visual specs (future)
```

---

## 🔴 **URGENT: Replace Placeholder Icons**

### **Current Placeholders (Replace These First)**
- `icons/system/alliance-24-default.svg` - Alliance/team icon
- `icons/system/venture-24-default.svg` - Project/venture icon  
- `icons/system/mission-24-default.svg` - Mission/task icon
- `icons/system/orb-24-default.svg` - Platform currency icon
- `icons/navigation/home-24-default.svg` - Home navigation
- `icons/navigation/dashboard-24-default.svg` - Dashboard navigation
- `icons/social/ally-24-default.svg` - Ally connection icon
- `icons/social/message-24-default.svg` - Messaging icon

### **Migrated Icons (Review & Update)**
- `icons/system/venture-app-24-default.svg` - App project type
- `icons/system/venture-game-24-default.svg` - Game project type
- `icons/system/venture-website-24-default.svg` - Website project type
- `icons/system/venture-music-24-default.svg` - Music project type
- `icons/system/venture-art-24-default.svg` - Art project type

---

## 📋 **Icon Standards**

### **Technical Requirements**
- **Size**: 24x24px (provide 16px, 20px, 32px variants if needed)
- **Format**: Optimized SVG
- **Colors**: Use `currentColor` for theme compatibility
- **Accessibility**: Include `<title>` and `<desc>` elements

### **Design Guidelines**
- **Style**: Modern, clean, professional
- **Weight**: Medium stroke (2px recommended)
- **Metaphors**: Clear, universally understood

---

## 🚀 **How to Add/Update Assets**

1. **Design Your Asset** - Follow standards above
2. **Save in Correct Location** - Use proper directory and naming
3. **Commit to Repository** - Push changes to git
4. **Automatic Integration** - AI agents detect and implement changes

---

## 📞 **Need Help?**

See the complete asset checklist: `docs/design-system/DESIGN_TEAM_ASSET_CHECKLIST.md`

**Your designs directly control the platform. Make them great!** 🎨
