# Design Team Asset Checklist
**Assets That Need Design Team Attention**

## 🎯 **Overview**

This document lists all assets that need to be created or replaced by the design team. Placeholder assets have been created in the correct locations - your job is to replace them with proper designs.

---

## 📁 **Asset Locations & Status**

### **🔴 CRITICAL - System Icons (Replace Placeholders)**

#### **Location**: `client/src/assets/design-system/icons/system/`

| Icon | File | Status | Description |
|------|------|--------|-------------|
| Alliance | `alliance-24-default.svg` | 🔴 **PLACEHOLDER** | Main alliance/team icon |
| Venture | `venture-24-default.svg` | 🔴 **PLACEHOLDER** | Project/venture icon |
| Mission | `mission-24-default.svg` | 🔴 **PLACEHOLDER** | Internal mission icon |
| ORB Currency | `orb-24-default.svg` | 🔴 **PLACEHOLDER** | Platform currency icon |

#### **✅ Migrated Project Type Icons (Review & Update)**
| Icon | File | Status | Description |
|------|------|--------|-------------|
| App Venture | `venture-app-24-default.svg` | ✅ **MIGRATED** | App/software project icon |
| Game Venture | `venture-game-24-default.svg` | ✅ **MIGRATED** | Game project icon |
| Website Venture | `venture-website-24-default.svg` | ✅ **MIGRATED** | Website project icon |
| Music Venture | `venture-music-24-default.svg` | ✅ **MIGRATED** | Music project icon |
| Art Venture | `venture-art-24-default.svg` | ✅ **MIGRATED** | Art project icon |

### **🟡 MEDIUM - Navigation Icons (Replace Placeholders)**

#### **Location**: `client/src/assets/design-system/icons/navigation/`

| Icon | File | Status | Description |
|------|------|--------|-------------|
| Home | `home-24-default.svg` | 🔴 **PLACEHOLDER** | Home/dashboard navigation |
| Dashboard | `dashboard-24-default.svg` | 🔴 **PLACEHOLDER** | Bento grid dashboard |

#### **🔴 MISSING - Create These Navigation Icons**
- `grid-view-24-default.svg` - Grid view toggle
- `world-view-24-default.svg` - Spatial world view toggle
- `zoom-in-24-default.svg` - Zoom in control
- `zoom-out-24-default.svg` - Zoom out control
- `profile-24-default.svg` - User profile navigation
- `settings-24-default.svg` - Settings navigation

### **🟡 MEDIUM - Social Icons (Replace Placeholders)**

#### **Location**: `client/src/assets/design-system/icons/social/`

| Icon | File | Status | Description |
|------|------|--------|-------------|
| Ally | `ally-24-default.svg` | 🔴 **PLACEHOLDER** | Ally connection icon |
| Message | `message-24-default.svg` | 🔴 **PLACEHOLDER** | Direct messaging icon |

#### **🔴 MISSING - Create These Social Icons**
- `endorse-24-default.svg` - Skill endorsement icon
- `connect-24-default.svg` - Connection request icon
- `network-24-default.svg` - Professional network icon
- `collaboration-24-default.svg` - Collaboration indicator

### **🟢 LOW - Action Icons (Create New)**

#### **Location**: `client/src/assets/design-system/icons/actions/`

#### **🔴 MISSING - Create These Action Icons**
- `create-24-default.svg` - Create new item
- `edit-24-default.svg` - Edit existing item
- `delete-24-default.svg` - Delete item
- `share-24-default.svg` - Share functionality
- `save-24-default.svg` - Save changes
- `cancel-24-default.svg` - Cancel action

---

## 📐 **Wireframe Exports Needed**

### **Location**: `client/src/assets/wireframes/exports/`

#### **🔴 HIGH PRIORITY - Create These Wireframe Exports**
1. **Alliance Dashboard** - `alliance-dashboard-desktop.png`
2. **Gamification Dashboard** - `gamification-dashboard-desktop.png`
3. **Social Network** - `ally-networking-desktop.png`
4. **Navigation System** - `spatial-navigation-desktop.png`
5. **Mission Board** - `mission-board-desktop.png`

#### **🟡 MEDIUM PRIORITY - Mobile Wireframes**
1. **Alliance Dashboard Mobile** - `alliance-dashboard-mobile.png`
2. **Gamification Mobile** - `gamification-dashboard-mobile.png`
3. **Social Network Mobile** - `ally-networking-mobile.png`

---

## 🎨 **Brand Assets Needed**

### **Location**: `client/src/assets/brand/logos/`

#### **🔴 MISSING - Create These Brand Assets**
- `royaltea-logo.svg` - Main Royaltea logo
- `royaltea-icon.svg` - Icon-only version
- `royaltea-wordmark.svg` - Text-only version

### **Location**: `client/src/assets/brand/patterns/`

#### **🟡 OPTIONAL - Background Elements**
- `background-patterns.svg` - Subtle background patterns
- `decorative-elements.svg` - UI decoration elements

---

## 📋 **Icon Design Standards**

### **Technical Requirements**
- **Size**: 24x24px default (provide 16px, 20px, 32px variants if needed)
- **Format**: Optimized SVG with clean paths
- **Colors**: Use `currentColor` for themeable icons
- **Accessibility**: Include `<title>` and `<desc>` elements

### **Design Guidelines**
- **Style**: Consistent with platform aesthetic (modern, clean, professional)
- **Weight**: Medium stroke weight (2px recommended)
- **Corners**: Slightly rounded for friendliness
- **Metaphors**: Clear, universally understood symbols

### **Example Icon Structure**
```svg
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <title>Alliance Icon</title>
  <desc>Icon representing team alliances and collaboration</desc>
  <!-- Your icon paths here -->
  <path d="..." stroke="currentColor" stroke-width="2" fill="none"/>
</svg>
```

---

## 🚀 **Priority Order for Design Team**

### **Week 1 - Critical System Icons**
1. **Alliance Icon** - Core platform concept
2. **Venture Icon** - Project representation
3. **Mission Icon** - Task management
4. **ORB Icon** - Platform currency

### **Week 2 - Navigation & Social**
1. **Navigation Icons** - Grid view, world view, zoom controls
2. **Social Icons** - Ally, endorse, connect, message
3. **Action Icons** - Create, edit, delete, share

### **Week 3 - Wireframes & Brand**
1. **Key Wireframe Exports** - Alliance dashboard, gamification dashboard
2. **Brand Assets** - Royaltea logo variations
3. **Mobile Wireframes** - Responsive versions

---

## ✅ **How to Replace Placeholder Assets**

### **Step 1: Design Your Icon**
- Create icon following design standards above
- Export as optimized SVG
- Test at different sizes (16px, 24px, 32px)

### **Step 2: Replace Placeholder File**
- Navigate to the correct directory
- Replace the existing placeholder file
- Keep the exact same filename

### **Step 3: Commit Changes**
- Commit your new asset to the repository
- AI agents will automatically detect and integrate the new asset

### **Step 4: Verify Integration**
- Check that the icon appears correctly in the UI
- Ensure it works with light/dark themes
- Validate accessibility attributes

---

## 📞 **Questions or Issues?**

If you need clarification on any asset requirements:
1. Check the corresponding system specification in `docs/design-system/systems/`
2. Review the wireframes in `docs/wireframes/`
3. Look at the asset management guide in `docs/design-system/asset-management.md`

---

**🎯 Goal**: Replace all placeholder assets with professional, cohesive designs that represent the Royaltea platform's vision of fair, transparent creative collaboration.**
