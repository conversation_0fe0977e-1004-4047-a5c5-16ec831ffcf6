# Ally Networking Wireframe
**Professional Networking and Connection Management**

## 📋 Wireframe Information
- **Purpose**: Professional networking, ally connections, and skill endorsements
- **User Access**: All authenticated users
- **Navigation Context**: Accessible from main dashboard and spatial navigation
- **Priority**: High - Core social platform feature
- **Implementation Status**: 🔴 Not Implemented

---

## 🎯 Overview

**[Design Team: This wireframe shows the complete professional networking experience]**

The Ally Networking interface enables users to build professional networks, connect with allies, endorse skills, and discover collaboration opportunities within the Royaltea platform.

### **Core Functionality**
- Ally connection management (friend requests)
- Skill endorsements and recommendations
- Professional profile discovery
- Collaboration history tracking
- Network analytics and insights

---

## 📱 Layout Wireframes

### **Desktop Layout (1200px+)**
```
┌─────────────────────────────────────────────────────────────────────────────┐
│ 🤝 Ally Network                                      [🔍 Search] [⚙️ Settings] │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────┐ ┌─────────────────────────────────────────────────────┐ │
│ │ 👥 Your Allies  │ │ 🌟 Discover New Allies                             │ │
│ │ (23 connections)│ │                                                     │ │
│ │                 │ │ 👤 David Kim                                        │ │
│ │ 🟢 Sarah Johnson│ │ Full-Stack Developer                                │ │
│ │ Frontend Dev    │ │ Skills: React, Node.js, TypeScript                 │ │
│ │ ⭐ Endorse      │ │ Mutual Allies: 3                                    │ │
│ │ 💬 Message      │ │ [Connect] [View Profile]                            │ │
│ │                 │ │                                                     │ │
│ │ 🟡 Mike Chen    │ │ 👤 Lisa Wong                                        │ │
│ │ Backend Dev     │ │ UI/UX Designer                                      │ │
│ │ ⭐ Endorse      │ │ Skills: Figma, Adobe XD, User Research             │ │
│ │ 💬 Message      │ │ Mutual Allies: 1                                    │ │
│ │                 │ │ [Connect] [View Profile]                            │ │
│ │ ⚫ Alex Rivera  │ │                                                     │ │
│ │ DevOps Engineer │ │ 👤 Emma Rodriguez                                   │ │
│ │ ⭐ Endorse      │ │ Project Manager                                     │ │
│ │ 💬 Message      │ │ Skills: Agile, Scrum, Team Leadership              │ │
│ │                 │ │ Mutual Allies: 2                                    │ │
│ │ [View All]      │ │ [Connect] [View Profile]                            │ │
│ │ [Find Allies]   │ │                                                     │ │
│ └─────────────────┘ │ [Load More Suggestions]                             │ │
│                     └─────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │ 📨 Connection Requests                                                  │ │
│ │                                                                         │ │
│ │ 👤 James Wilson wants to connect                                       │ │
│ │ "I'd love to collaborate on React projects with your team"             │ │
│ │ Skills: React, GraphQL, AWS    Mutual Allies: 2                        │ │
│ │ [Accept] [Decline] [View Profile] [Message]                            │ │
│ │                                                                         │ │
│ │ 👤 Maria Garcia wants to connect                                       │ │
│ │ "Interested in joining forces for mobile development"                  │ │
│ │ Skills: React Native, iOS, Android    Mutual Allies: 1                 │ │
│ │ [Accept] [Decline] [View Profile] [Message]                            │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

### **Mobile Layout (< 768px)**
```
┌─────────────────────────────────────┐
│ 🤝 Ally Network        [🔍] [⚙️]   │
├─────────────────────────────────────┤
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 👥 Your Allies (23)             │ │
│ │                                 │ │
│ │ 🟢 Sarah Johnson                │ │
│ │    Frontend Developer           │ │
│ │    React • TypeScript • UI/UX   │ │
│ │    [💬 Message] [⭐ Endorse]    │ │
│ │                                 │ │
│ │ 🟡 Mike Chen                    │ │
│ │    Backend Developer            │ │
│ │    Node.js • Python • AWS      │ │
│ │    [💬 Message] [⭐ Endorse]    │ │
│ │                                 │ │
│ │ [View All Allies]               │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 📨 Requests (2)                 │ │
│ │                                 │ │
│ │ 👤 James Wilson                 │ │
│ │ "Let's collaborate on React!"   │ │
│ │ [Accept] [Decline] [Profile]    │ │
│ │                                 │ │
│ │ 👤 Maria Garcia                 │ │
│ │ "Mobile development partner?"   │ │
│ │ [Accept] [Decline] [Profile]    │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 🌟 Discover Allies              │ │
│ │                                 │ │
│ │ 👤 David Kim                    │ │
│ │ Full-Stack Developer            │ │
│ │ 3 mutual allies                 │ │
│ │ [Connect] [View Profile]        │ │
│ │                                 │ │
│ │ [More Suggestions]              │ │
│ └─────────────────────────────────┘ │
│                                     │
└─────────────────────────────────────┘
```

---

## 🔄 User Flow

### **Ally Connection Flow**
```mermaid
graph TD
    A[User Opens Ally Network] --> B[Browse Ally Suggestions]
    B --> C[Find Interesting Person]
    C --> D[View Their Profile]
    D --> E[Send Connection Request]
    E --> F[Add Personal Message]
    F --> G[Send Request]
    G --> H[Request Sent Notification]
    H --> I[Other User Receives Request]
    I --> J[Accept/Decline Decision]
    J --> K[Connection Established]
    K --> L[Can Message & Collaborate]
```

### **Skill Endorsement Flow**
```mermaid
graph TD
    A[View Ally Profile] --> B[See Skills Section]
    B --> C[Click 'Endorse Skill']
    C --> D[Select Skill Level]
    D --> E[Add Optional Message]
    E --> F[Submit Endorsement]
    F --> G[Endorsement Recorded]
    G --> H[Notification Sent]
    H --> I[Skill Rating Updated]
```

---

## 🧩 Component Breakdown

### **Ally List Component**
- **Online Status Indicators**: Green (online), Yellow (away), Gray (offline)
- **Professional Info**: Title, key skills, experience level
- **Quick Actions**: Message and Endorse buttons
- **Ally Count**: Display total number of connections
- **Search/Filter**: Find specific allies quickly

### **Ally Discovery Component**
- **Skill-based Matching**: Suggest allies with complementary skills
- **Mutual Connections**: Show shared allies
- **Professional Compatibility**: Match based on experience and interests
- **Industry Filtering**: Find allies in specific fields
- **Location Proximity**: Optional location-based suggestions

### **Connection Requests Component**
- **Request Details**: Sender info, personal message, mutual connections
- **Skill Overview**: Quick view of requester's skills
- **Quick Actions**: Accept, decline, view profile, message
- **Batch Actions**: Accept/decline multiple requests
- **Request Count**: Badge showing pending count

---

## 📱 Responsive Behavior

### **Mobile (< 768px)**
- **Single Column Layout**: Stack all components vertically
- **Swipeable Ally Cards**: Horizontal swipe for actions
- **Simplified Discovery**: Condensed ally suggestions
- **Bottom Sheet Requests**: Slide-up interface for pending requests
- **Touch-Optimized Buttons**: Larger tap targets for mobile

### **Tablet (768px - 1024px)**
- **Two Column Layout**: Allies on left, discovery on right
- **Collapsible Sections**: Expandable ally groups
- **Touch-Friendly Interface**: Optimized for tablet interaction
- **Grid View Option**: Card-based ally display

### **Desktop (1024px+)**
- **Three Column Layout**: Allies, discovery, and requests
- **Hover States**: Rich hover interactions and tooltips
- **Keyboard Shortcuts**: Quick navigation and actions
- **Advanced Filtering**: Detailed search and filter options

---

## 🎨 Visual Design Requirements

### **Color Scheme**
- **Online Status**: Green (#22c55e) for online allies
- **Away Status**: Yellow (#eab308) for away allies
- **Offline Status**: Gray (#6b7280) for offline allies
- **Connection Actions**: Royal purple (#8b5cf6) for connect buttons
- **Request Actions**: Tea green (#22c55e) for accept, red (#ef4444) for decline

### **Typography**
- **Ally Names**: font-semibold text-lg for primary identification
- **Professional Titles**: text-base text-gray-600 for role information
- **Skills**: text-sm with skill badges/chips
- **Request Messages**: text-sm italic for personal messages

### **Animations**
- **Status Changes**: Smooth color transitions for online status
- **Connection Actions**: Slide-out animation when connecting
- **Request Actions**: Fade out animation when accepting/declining
- **Hover Effects**: Subtle scale and shadow changes

---

## 🔧 Technical Implementation

### **Props & State**
```javascript
const AllyNetworking = ({
  currentUser,
  allies = [],
  suggestions = [],
  connectionRequests = [],
  onSendRequest,
  onAcceptRequest,
  onDeclineRequest,
  onEndorseSkill
}) => {
  const [selectedAlly, setSelectedAlly] = useState(null);
  const [showRequests, setShowRequests] = useState(false);
  const [discoveryFilter, setDiscoveryFilter] = useState('all');
};
```

### **Data Requirements**
- **Ally Connections**: User relationships and professional info
- **Connection Requests**: Incoming and outgoing requests
- **Skill Endorsements**: Skill ratings and endorsements
- **Professional Profiles**: Skills, experience, availability
- **Mutual Connections**: Shared network analysis

---

## ♿ Accessibility Features

### **Screen Reader Support**
- **Ally Status**: "Sarah Johnson, Frontend Developer, online, 5 endorsements"
- **Connection Actions**: "Send connection request to David Kim"
- **Request Actions**: "Accept connection request from James Wilson"
- **Navigation**: Clear heading structure and landmarks

### **Keyboard Navigation**
- **Tab Order**: Logical tab sequence through all interactive elements
- **Keyboard Shortcuts**: 
  - `C` to connect with suggested ally
  - `A` to accept requests
  - `D` to decline requests
  - `E` to endorse skills
- **Focus Indicators**: Clear visual focus states
- **Skip Links**: Quick navigation to main content areas

---

**[Design Team: This ally networking wireframe creates a professional networking experience focused on skill-based connections and meaningful professional relationships.]**
